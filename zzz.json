{"from": 0, "size": 96, "query": {"function_score": {"query": {"bool": {"must": [{"bool": {"should": [{"match": {"catchAll_fi": {"query": "de<PERSON><PERSON><PERSON>", "operator": "AND", "analyzer": "whitespace_lowercase", "fuzziness": "0", "prefix_length": 0, "max_expansions": 1, "minimum_should_match": "100%", "fuzzy_transpositions": false, "lenient": false, "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "boost": 1, "_name": "Fuzzy"}}}, {"match_phrase_prefix": {"catchAll_fi": {"query": "de<PERSON><PERSON><PERSON>", "analyzer": "standard", "slop": 0, "max_expansions": 3, "zero_terms_query": "NONE", "boost": 1, "_name": "Prefix"}}}, {"match": {"codes": {"query": "de<PERSON><PERSON><PERSON>", "operator": "OR", "analyzer": "keyword_lowercase", "fuzziness": "0", "prefix_length": 0, "max_expansions": 50, "fuzzy_transpositions": false, "lenient": false, "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "boost": 1, "_name": "Code"}}}], "adjust_pure_negative": true, "boost": 0}}], "filter": [{"terms": {"facets.archived": ["false"], "boost": 1}}, {"term": {"visibleInOnnshop": {"value": true, "boost": 1}}}], "should": [{"multi_match": {"query": "de<PERSON><PERSON><PERSON>", "fields": ["keywords.fi^1.0", "keywords.fi.middle^1.0", "keywords.fi.prefix^1.0"], "type": "best_fields", "operator": "OR", "slop": 0, "prefix_length": 0, "max_expansions": 50, "minimum_should_match": "1", "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 1}}, {"multi_match": {"query": "de<PERSON><PERSON><PERSON>", "fields": ["brand^1.0", "vendor^1.0"], "type": "best_fields", "operator": "OR", "slop": 0, "prefix_length": 0, "max_expansions": 50, "minimum_should_match": "1", "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 5}}, {"multi_match": {"query": "de<PERSON><PERSON><PERSON>", "fields": ["categoryNames.fi^1.0", "categoryNames.fi.middle^1.0", "categoryNames.fi.prefix^1.0"], "type": "best_fields", "operator": "OR", "slop": 0, "prefix_length": 0, "max_expansions": 50, "minimum_should_match": "1", "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 0.8}}, {"match": {"name.fi": {"query": "de<PERSON><PERSON><PERSON>", "operator": "OR", "analyzer": "whitespace_lowercase", "prefix_length": 0, "max_expansions": 50, "fuzzy_transpositions": true, "lenient": false, "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "boost": 6, "_name": "name words boost"}}}, {"multi_match": {"query": "de<PERSON><PERSON><PERSON>", "fields": ["eanCode^2.0", "eanCode.prefix3^1.0", "elecCode^2.0", "elecCode.prefix3^1.2", "manufacturerCode^2.0", "plcCode^2.0", "plcCode.prefix3^1.1", "sapCode^2.0", "vendorCode^2.0"], "type": "best_fields", "operator": "OR", "slop": 0, "prefix_length": 0, "max_expansions": 50, "minimum_should_match": "1", "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 1.5, "_name": "code boost"}}, {"multi_match": {"query": "de<PERSON><PERSON><PERSON>", "fields": ["nameWords.fi^1.0", "nameWords.fi.middle^1.0", "nameWords.fi.prefix^1.0", "shortDescriptionWords.fi^1.0", "shortDescriptionWords.fi.middle^1.0", "shortDescriptionWords.fi.prefix^1.0"], "type": "most_fields", "operator": "OR", "slop": 0, "prefix_length": 0, "max_expansions": 50, "minimum_should_match": "1", "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "fuzzy_transpositions": true, "boost": 3}}], "adjust_pure_negative": true, "boost": 1}}, "functions": [{"filter": {"term": {"productType": {"value": "WAREHOUSE", "boost": 1}}}, "weight": 1.2}, {"filter": {"match_all": {"boost": 1}}, "script_score": {"script": {"source": "params.base + (doc['normalizedTotalSalesVolume'].value * params.scale)", "lang": "painless", "params": {"base": 1, "scale": 2}}}}, {"filter": {"term": {"saleable": {"value": true, "boost": 1}}}, "weight": 2}, {"filter": {"match": {"soldOut": {"query": true, "operator": "OR", "prefix_length": 0, "max_expansions": 50, "fuzzy_transpositions": true, "lenient": false, "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "boost": 1}}}, "weight": 0.1}, {"filter": {"terms": {"facets.brand": ["ONNLINE", "ONNINEN", "OPAL", "DIMPLEX", "MIDEA", "PROF"], "boost": 1}}, "weight": 1.2}, {"filter": {"term": {"saleable": {"value": false, "boost": 1}}}, "weight": 0.01}, {"filter": {"term": {"discontinued": {"value": true, "boost": 1}}}, "weight": 0.01}], "score_mode": "multiply", "max_boost": 3.4028235e+38, "boost": 1}}, "_source": {"includes": ["sapCode", "generalName.fi", "technicalName.fi", "priced", "own<PERSON>rand", "normalizedTotalSalesVolume", "keywords", "orderHistory", "eanCode", "hepacCode", "elecCode", "manufacturerCode", "plcCode", "<PERSON><PERSON><PERSON>", "technicalName", "categoryNames", "brand", "totalSalesVolume", "normalizedTotalSalesVolume", "description", "shortDescription"], "excludes": []}, "sort": [{"_score": {"order": "desc"}}, {"sort.sap": {"order": "asc", "unmapped_type": "keyword"}}], "aggregations": {"categoryPaths": {"terms": {"field": "categoryPaths", "size": 1000, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": [{"_count": "desc"}, {"_key": "asc"}], "include": "/[^/]+?"}}, "categoryFilter": {"terms": {"field": "facets.categoryFilter", "size": 1000, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "desc"}}}, "availability": {"terms": {"field": "facets.availability", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "archived": {"global": {}, "aggregations": {"archived": {"filter": {"bool": {"must": [{"bool": {"should": [{"match": {"catchAll_fi": {"query": "de<PERSON><PERSON><PERSON>", "operator": "AND", "analyzer": "whitespace_lowercase", "fuzziness": "0", "prefix_length": 0, "max_expansions": 1, "minimum_should_match": "100%", "fuzzy_transpositions": false, "lenient": false, "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "boost": 1, "_name": "Fuzzy"}}}, {"match_phrase_prefix": {"catchAll_fi": {"query": "de<PERSON><PERSON><PERSON>", "analyzer": "standard", "slop": 0, "max_expansions": 3, "zero_terms_query": "NONE", "boost": 1, "_name": "Prefix"}}}, {"match": {"codes": {"query": "de<PERSON><PERSON><PERSON>", "operator": "OR", "analyzer": "keyword_lowercase", "fuzziness": "0", "prefix_length": 0, "max_expansions": 50, "fuzzy_transpositions": false, "lenient": false, "zero_terms_query": "NONE", "auto_generate_synonyms_phrase_query": true, "boost": 1, "_name": "Code"}}}], "adjust_pure_negative": true, "boost": 0}}], "filter": [{"term": {"visibleInOnnshop": {"value": true, "boost": 1}}}], "adjust_pure_negative": true, "boost": 1}}, "aggregations": {"archived": {"terms": {"field": "facets.archived", "size": 2, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}}}}}, "brand": {"terms": {"field": "facets.brand", "size": 1000, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "certificates": {"terms": {"field": "localizedFacets.certificates.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "originLabellingCertificates": {"terms": {"field": "localizedFacets.originLabellingCertificates.fi", "size": 1, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "sustainabilityLabels": {"terms": {"field": "localizedFacets.sustainabilityLabels.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "catalog": {"terms": {"field": "localizedFacets.catalog.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": [{"_count": "desc"}, {"_key": "asc"}]}}, "marketing": {"terms": {"field": "facets.marketing", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "sparePart": {"terms": {"field": "facets.sparePart", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "countryOfOrigin": {"terms": {"field": "countryOf<PERSON><PERSON>in", "size": 50, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "classifications": {"terms": {"field": "classifications", "size": 20, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": [{"_count": "desc"}, {"_key": "asc"}]}}, "ETIM_EF002200": {"terms": {"field": "technicalAttributes.ETIM_EF002200.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000486": {"terms": {"field": "technicalAttributes.ETIM_EF000486.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000076": {"terms": {"field": "technicalAttributes.ETIM_EF000076.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000081": {"terms": {"field": "technicalAttributes.ETIM_EF000081.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000344": {"terms": {"field": "technicalAttributes.ETIM_EF000344.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000002": {"terms": {"field": "technicalAttributes.ETIM_EF000002.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000551": {"terms": {"field": "technicalAttributes.ETIM_EF000551.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000041": {"terms": {"field": "technicalAttributes.ETIM_EF000041.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000008": {"terms": {"field": "technicalAttributes.ETIM_EF000008.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF000139": {"terms": {"field": "technicalAttributes.ETIM_EF000139.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}, "ETIM_EF002169": {"terms": {"field": "technicalAttributes.ETIM_EF002169.fi", "size": 100, "min_doc_count": 1, "shard_min_doc_count": 0, "show_term_doc_count_error": false, "order": {"_key": "asc"}}}}}