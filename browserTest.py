# run 2 browsers under selenium and sarch for all given terms
import time

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

index_types = ["TIERED", "EX1"]
# current_sales_org = "4000"
# current_sales_org = "2000"
current_sales_org = "1000"
notDone = False
# url= "http://localhost-onnshop-no:8889/"
# url= "https://onninen.fi/"
urls= {
    "1000":"https://onninen.fi/",
    "2000":"https://onninen.no/"
    }
url = urls[current_sales_org]

def load_terms(filename):
    terms_without_isok = []
    terms_with_isok = []
    with open(filename, "r") as f:
        for line in f:
            line = line.strip()  # Remove leading/trailing whitespace
            if "#" in line:
                line = line[: line.index("#")]  # Remove comment part
            if "ISOK" in line:
                terms_with_isok.append(line)
            else:
                terms_without_isok.append(line)
    return terms_without_isok, terms_with_isok


def load_terms_from_file(file_path, skip_isok=False, start_from=0):
    with open(file_path, "r") as file:
        lines = file.readlines()

        cleaned_lines = []
        count = 0
        for line in lines:
            count += 1
            if start_from > count:
                continue
            stripped_line = line.strip()
            if not stripped_line:
                continue
            main_part, *comment_part = stripped_line.split("#", 1)
            if skip_isok and comment_part and "ISOK" in comment_part[0]:
                continue
            cleaned_lines.append(main_part.strip())

    return cleaned_lines


# search_terms = load_terms_from_file(f'search_terms_{current_sales_org}_todo.txt', False, 0)
search_terms = load_terms_from_file(f"search_terms_{current_sales_org}.txt", False, 0)

chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument("--start-maximized")
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--password-store=gnome-libsecret")

# setup correct url and search engine
driverS = webdriver.Chrome(options=chrome_options)
driverS.get('http://localhost-onnshop-fi:8889/')
# driverS.execute_script(f'window.searchEngine = "FORCE-SEARCH-ENGINE_{index_types[0]}"')
driverS.execute_script(f'window.searchEngine = "FORCE-SEARCH-ENGINE_EX1"')

driverE = webdriver.Chrome(options=chrome_options)
driverE.get('https://onninen.fi/')
# driverE.execute_script(f'window.searchEngine = "FORCE-SEARCH-ENGINE_{index_types[1]}"')
driverE.execute_script(f'window.searchEngine = "FORCE-SEARCH-ENGINE_EX1"')

time.sleep(3)


for term in search_terms:
    print(term)
    driverS.find_element(By.ID, "product-search__search-input").send_keys(term)
    time.sleep(1)
    driverS.find_element(By.CLASS_NAME, "search-input__button").click()

    driverE.find_element(By.ID, "product-search__search-input").send_keys(term)
    time.sleep(1)
    driverE.find_element(By.CLASS_NAME, "search-input__button").click()

    if notDone:
        time.sleep(2)
        driverS.find_element(By.XPATH, '//*[@aria-label="List view"]').click()
        driverE.find_element(By.XPATH, '//*[@aria-label="List view"]').click()
        notDone = False

    try:
        waitS = WebDriverWait(driverS, 1)
        buttonS = waitS.until(
            EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[data-test="location-consent-cancel"]')
            )
        )
        buttonS.click()
        waitE = WebDriverWait(driverE, 1)
        buttonE = waitE.until(
            EC.presence_of_element_located(
                (By.CSS_SELECTOR, '[data-test="location-consent-cancel"]')
            )
        )
        buttonE.click()
    except:
        pass
    # time.sleep(3)
    input()
    driverS.find_element(By.CLASS_NAME, "search-input__button--clear").click()
    driverE.find_element(By.CLASS_NAME, "search-input__button--clear").click()
