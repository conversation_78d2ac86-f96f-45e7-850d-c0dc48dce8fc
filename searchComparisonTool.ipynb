import requests
import socks
from scipy import stats
import numpy as np
import pprint
import time
import difflib
import pickle
from itertools import combinations
import datetime
import os
from re import I
from selenium import  webdriver
import subprocess
from listCmp import levenshtein_distance, overlap, similarity_score
from bastion_proxy import proxy_settings
import json
import gzip
from lolviz import *

test_case = 'no_keyword'
file_suffix=''
log_dir=f'search_log_{test_case}{file_suffix}'

# Set the target URL
#opensearch_prod_url = 'https://product-search-os.prod.b2becom.keskodev.zone'
# search_url = 'https://search-ecs.prod.b2becom.keskodev.zone/rest'

search_url = 'http://localhost:8091/rest'

# select search types to compare
# index_types = ['STANDARD', 'TIERED', 'EXPERIMENTAL', 'EX1']
# index_types = ['TIERED', 'EX1']
index_types = ['EX1', 'EX2']
# index_types = ['EX1']
# current_sales_org = "4000"
# current_sales_org = "2000"
current_sales_org = "1000"
channel = "ONNSHOP"
lang = {'1000':'fi', '2000':'no', '1500':'sv'}

def load_terms_from_file(file_path, skip_isok = False):
    with open(file_path, 'r') as file:
        lines = file.readlines()

        cleaned_lines = []
        for line in lines:
            stripped_line = line.strip()
            if not stripped_line:
                continue
            main_part, *comment_part = stripped_line.split('#', 1)
            if skip_isok and comment_part and 'ISOK' in comment_part[0]:
                continue
            cleaned_lines.append(main_part.strip())

    return cleaned_lines


def cut_to_shortest(list1, list2):
    min_length = min(len(list1), len(list2))

    cut_list1 = list1[:min_length]
    cut_list2 = list2[:min_length]

    return cut_list1, cut_list2

def get_indices(url):
    indices_url = f'{url}/_cat/indices'
    # HTTPS request with the SOCKS proxy
    response = requests.get(indices_url, proxies=proxy_settings)
    if response.status_code != 200:
        print(f"Error: {response.status_code}")
        print(response.text)
    else:
        return response.text

# set for anonymous account, update if needed
# set for local search service instance
def execute_search(term, sales_org, channel, type):
  proxy= proxy_settings if 'prod' in search_url else None
  url = f'{search_url}/v2/{sales_org}/{channel}/search'
  search_query = {
    "loggedIn": False,
    "includeTechnicalAttributeFacets": True,
    # "assortmentIds": [
    #   "string"
    # ],
    #   "assortmentIdsToBoost": [
    #     "string"
    #   ],
    "accountId": "anonymous",
    # "customerId": "**********",
    #   "contractId": "string",
    # "industryCode1": "1IND10101",
    # "industryCode2": "2IND001",
    # "industryCode3": "",
    # "industryCode4": "",
    # "industryCode5": "",
    "promoteTopOwnBrandProduct": True,
    "query": {
      "term": term,
      "filters":["archived=false"],
    },
    "pagination": {
      "from": 0,
      "size": 5048,
    }
  }

  params = {
    'searchEngine': type
  }
  headers = {'Content-Type': 'application/json', 'X-Locale': lang[sales_org]}
  response = requests.post(url, json=search_query, params=params, headers=headers, proxies=proxy)
  if response.status_code == 200:
    return response.json()
  else:
    print(f'Error: {response.status_code}')
    print(response.text)

# execute search for all search terms and all index types, compare the results pairwise
def search_term_check(search_terms, sales_org, channel):
    result= {}
    for term in search_terms:
        cmp_result = {}
        term_key = term #.replace(' ', '_')
        result[term_key] = {}
        for index_type in index_types:
            sr = execute_search(term, sales_org, channel, index_type)
            hits = sr['result']['hits']
            cmp_result[index_type] = [item['code'] for item in sr['result']['products']]
            result[term_key][index_type[:3]] = hits
            result[term_key][index_type[:3]+'data'] = sr['result']['products']
        for comb in list(combinations(index_types,2)):
            i1 = comb[0]
            i2 = comb[1]
            key = f'{i1[:3]}_{i2[:3]}'
            result[term][key] = compare_results(cmp_result[i1], cmp_result[i2])
    return result

# for testing material code prefix search
def get_test_materials_and_prefixes(materials):
    test_materials = []
    prefixes = []
    for material in materials:
        test_materials.append(material['_source']['sapCode'])
        test_materials.append(material['_source']['plcCode'][0])
        p = material['_source']['plcCode'][0]
        prefixes.append(p[:5])
    return test_materials, prefixes

# for testing full code search
def material_full_code_check(materials, sales_org, channel):
    results = {}
    for test_material in materials:
        results[test_material] = {}
        for index_type in index_types:
            sr = execute_search(test_material, sales_org, channel, index_type)
            hits = sr['result']['hits']
            results[test_material][index_type[:3]] = hits
    return results

# compare result sets with different metrics
# 1: https://en.wikipedia.org/wiki/Spearman%27s_rank_correlation_coefficient
# 2: https://en.wikipedia.org/wiki/Levenshtein_distance
# 3: normalized result set overlap
def compare_results(list_a, list_b):
    result = {}
    if (len(list_a) == 0 or len(list_b) == 0):
        return result
    l1,l2 = cut_to_shortest(list_a, list_b)
    sprarman = stats.spearmanr(l1,l2)
    result['sr'] = [round(float(sprarman.correlation),2), round(float(sprarman.pvalue),2)]
    result['lv'] = levenshtein_distance(l1,l2)
    result['ov'] = overlap(list_a, list_b)
    result['ss'] = round(similarity_score(list_a, list_b),2)
    sm = difflib.SequenceMatcher(None, list_a, list_b)
    result['df'] = round(sm.ratio(),2)
    return result

def material_code_prefix_check(materal_prefixes, sales_org, channel):
    results = {}
    for test_material in materal_prefixes:
        results[test_material] = {}
        cmp_result = {}
        for index_type in index_types:
            sr = execute_search(test_material, sales_org, channel, index_type)
            hits = sr['result']['hits']
            cmp_result[index_type] = [item['code'] for item in sr['result']['products']]
            results[test_material][index_type[:3]] = hits
            results[test_material][index_type[:3]+'data'] = sr['result']['products']

        for comb in list(combinations(index_types,2)):
            i1 = comb[0]
            i2 = comb[1]
            key = f'{i1[:3]}_{i2[:3]}'
            results[test_material][key] = compare_results(cmp_result[i1], cmp_result[i2])

    return results

# save as pickle and gzipped json
def save_data_to_file(data, directory='search_test_log', filename_prefix='search_log'):
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    filename = os.path.join(directory, f"{filename_prefix}_{timestamp}.pickle")
    filename_json = os.path.join(directory, f"{filename_prefix}_{timestamp}.json.gz")
    os.makedirs(directory, exist_ok=True)
    with open(filename, 'wb') as file:
        pickle.dump(data, file)
    with gzip.open(filename_json, "wt", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"Data saved to files: {filename}, {filename_json}")

# store current branch
def git_hash(directory="/home/<USER>/Projects/kesko/b2becom-search"):
    try:
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            cwd=directory,
            text=True,
            capture_output=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        return f"An error occurred while getting git hash: {e}"
    except FileNotFoundError:
        return "Git command not found."

# store git diff
def git_diff(directory ="/home/<USER>/Projects/kesko/b2becom-search"):
    try:
        result = subprocess.run(
            ["git", "diff"],
            cwd=directory,
            text=True,
            capture_output=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        return f"An error occurred while running git diff: {e}"
    except FileNotFoundError:
        return "Git command not found"

git_diff_string = git_diff();
git_hash_string = git_hash();

search_terms = load_terms_from_file(f'search_terms_{current_sales_org}{file_suffix}.txt')

search_term_check_results = search_term_check(search_terms, current_sales_org, channel)
#pprint.pprint(search_term_check_results)
search_term_check_log = {}
search_term_check_log['git_diff'] = git_diff_string
search_term_check_log['git_hash'] = git_hash_string
search_term_check_log['result'] = search_term_check_results;
save_data_to_file(search_term_check_log, log_dir, f'search_term_check_log{file_suffix}')

#print results
for i in search_term_check_results:
    print(i, '->', 'ex1:', search_term_check_results[i]['EX1'], ' | ex2:', search_term_check_results[i]['EX2'], ' m:', search_term_check_results[i]['EX1_EX2'])
    # print(i, '->', 't:', search_term_check_results[i]['TIE'], ' | e:', search_term_check_results[i]['EX1'])
    # print(i, 'e:', search_term_check_results[i]['EX1'])
    # print(i, '->', 's:', search_term_check_results[i]['STA'], ' | e:', search_term_check_results[i]['EX1'], '')

#print results
for i in search_term_check_results:
    print(i, ';', search_term_check_results[i]['EX1'],';', search_term_check_results[i]['EX2'], ';', search_term_check_results[i]['EX1_EX2'])
    # print(i, '->', 't:', search_term_check_results[i]['TIE'], ' | e:', search_term_check_results[i]['EX1'])
    # print(i, 'e:', search_term_check_results[i]['EX1'])
    # print(i, '->', 's:', search_term_check_results[i]['STA'], ' | e:', search_term_check_results[i]['EX1'], '')

r = search_term_check(['strips'], current_sales_org, channel)

stripsData= r.get('strips')

stripsData['EX1data']

stripsCodesEX1 = list(map(lambda x: x['code'],  stripsData['EX1data']))
stripsCodesTIE = list(map(lambda x: x['code'],  stripsData['EX2data']))

diff = list(set(stripsCodesTIE) - set(stripsCodesEX1))

print('ex1:', len(stripsCodesEX1), 'tie:', len(stripsCodesTIE))

diff

help(search_term_check_results)