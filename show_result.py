#!python

import sys
import pickle
import shutil
import requests
import traceback
from loguru import logger
from listCmp import levenshtein_distance, overlap
from bastion_proxy import proxy_settings
import json, gzip


sales_org = '1000'
sales_org = '2000'
# lang = 'no'
lang = 'fi'
# verbose = True
verbose = False
# commerceplus_url = 'https://commerceplus.prod.b2becom.keskodev.zone/rest'
commerceplus_url = 'http://localhost:6600/rest'

def print_separator():
    terminal_width = shutil.get_terminal_size().columns
    separator = '-' * terminal_width
    print(separator)

class Style():
    RED = '\033[31m'
    GREEN = '\033[32m'
    BLUE = '\033[34m'
    RESET = '\033[0m'
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

@logger.catch
def get_result_count(result, result_type = 'EX1'):
    try:
        return result[result_type]
    except Exception:
        #print(f"\t\t getResult KeyError: {result}")
        return 0

def get_result_data(result, result_type = 'EX1data'):
    try:
        return [ res['code'] for res in result[result_type]]
    except Exception:
        return []

def compare_numbers(numbers):
    min_number = min(numbers)
    max_number = max(numbers)
    if max_number - min_number > 10:
        return f"{numbers}  {Style.RED}Min: {min_number}, Max: {max_number}{Style.RESET}"
    else:
        return f"{numbers}"

def print_list_in_columns(strings):
    terminal_width = shutil.get_terminal_size().columns - 16
    column_width = terminal_width // len(strings)
    print('\t\t', end='')
    for i, string in enumerate(strings):
        end_char = '\n' if (i + 1) % len(strings) == 0 else ''
        print(f"{string:<{column_width}}", end=end_char)

#[[products from data set1],[products from data set2], [.....]]
def display_product_data(product_data, product_info):
    set_count = len(product_data)
    max_index = max([len(d) for d in product_data])
    lines = []
    for i in range(max_index):
        product_line = []
        for j in range(set_count):
            try:
                p = product_data[j][i]
                p_info = product_info[j][p['productCode']]
                product_line.append(f"{p['productCode']} {p['generalName']} {p['technicalName']} {p_info['score']} {p_info['boost']}")
            except IndexError:
                product_line.append("")
        lines.append(product_line)
    for l in lines:
        print_list_in_columns(l)

def get_product_data(sales_org, product_codes, lang):
    url = f'{commerceplus_url}/v2/{sales_org}/products/texts'
    proxy= proxy_settings if 'prod' in commerceplus_url else None
    query = product_codes
    headers = {
    "Accept": "application/json",
    "X-Locale": lang
    }
    response = requests.post(url, json=query, headers=headers, proxies=proxy)
    if response.status_code == 200:
        resp = response.json()
        return [resp[code] for code in product_codes]
    else:
        print(f'Error: {response.status_code}')
        print(response.text)

def print_term(term, term_data, verbose=False):
    chunk_keys = term_data.keys()
    print_separator()

    # result_stats = term_data.get("TIE_EX1", {}) 
    # result_count = [term_data.get("TIE"), term_data.get("EX1")]
    # result_info = [{p['code']:p for p in term_data.get("TIEdata")}, {p['code']:p for p in term_data.get("EX1data")}]
    # result_products = [[p['code'] for p in term_data.get("TIEdata")], [p['code'] for p in term_data.get("EX1data")]]
    
    result_stats = term_data.get("EX1_EX2", {}) 
    result_count = [term_data.get("EX1"), term_data.get("EX2")]
    result_info = [{p['code']:p for p in term_data.get("EX1data")}, {p['code']:p for p in term_data.get("EX2data")}]
    result_products = [[p['code'] for p in term_data.get("EX1data")], [p['code'] for p in term_data.get("EX2data")]]
    print(f"{Style.GREEN}{Style.BOLD}{term}{Style.RESET}: {compare_numbers(result_count)}", end='')
    print("  ", result_stats)

    # ll = '\n\t\t'.join(f"{sublist}" for sublist in result_products)
    ll = ''
    if verbose:
        product_data = [get_product_data(sales_org, code_set, lang) for code_set in result_products]
        display_product_data(product_data, result_info)
    else:
        print(f"\t\t{ll}")


def main():
    if len(sys.argv) != 2:
        print("Show single file SS1<->SS2, update code accordingly")
        print("Usage: python show_result.py <pickle_file_path>")
        sys.exit(1)

    try:
        filename = sys.argv[1]
        print('File:',filename)

        result = pickle.load(open(filename, "rb"))['result']
        # result = json.load(gzip.open(filename, "rt", encoding="utf-8"))['result']

        search_terms = result.keys()
        for term in search_terms:
            print_term(term, result[term], verbose=verbose)

    except Exception as e:
        print(f"An error occurred: {e}")
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
