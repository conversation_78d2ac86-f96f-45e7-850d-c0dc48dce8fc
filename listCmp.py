import math
import numpy as np

def overlap(list_a, list_b):
    try:
        return round(len(intersection(list_a, list_b))/max(len(list_a), len(list_b)), 2)
    except:
        return 'X'

def intersection(lst1, lst2):
    return list(set(lst1) & set(lst2))

def levenshtein_distance(word1, word2):
    m, n = len(word1), len(word2)
    if m == 0 or n == 0:
        return 'xxx'
    matrix = np.zeros((m+1, n+1), dtype=int)
    matrix[:, 0] = np.arange(m+1)
    matrix[0, :] = np.arange(n+1)
    for i in range(1, m+1):
        for j in range(1, n+1):
            if word1[i-1] == word2[j-1]:
                substitution_cost = 0
            else:
                substitution_cost = 1
            matrix[i, j] = min(
                matrix[i-1, j] + 1,                # deletion
                matrix[i, j-1] + 1,                # insertion
                matrix[i-1, j-1] + substitution_cost    # substitution
            )
    similarity = 1 - matrix[m, n] / max(m, n)
    return round(float(similarity), 2)

def similarity_score(list1, list2):
    max_len = max(len(list1), len(list2))
    score = 0.0
    total_weight = 0.0
    for i in range(max_len):
        weight = math.exp(-i)
        total_weight += weight
        if i < len(list1) and i < len(list2) and list1[i] == list2[i]:
            score += weight
    return round(float(score / total_weight)) if total_weight > 0 else 1.0  # 1.0 if both lists are empty
