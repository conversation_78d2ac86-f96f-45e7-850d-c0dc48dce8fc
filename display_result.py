#!python

import sys
import pickle
import shutil
import requests
import traceback
from loguru import logger
from listCmp import levenshtein_distance, overlap, similarity_score
from bastion_proxy import proxy_settings
import json, gzip

# commerceplus_url = 'https://commerceplus.prod.b2becom.keskodev.zone/rest'
commerceplus_url = 'http://localhost:6600/rest'

def print_separator():
    terminal_width = shutil.get_terminal_size().columns
    separator = '-' * terminal_width
    print(separator)

class Style():
    RED = '\033[31m'
    GREEN = '\033[32m'
    BLUE = '\033[34m'
    RESET = '\033[0m'
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

@logger.catch
def get_result_count(result, result_type = 'EX1'):
    try:
        return result[result_type]
    except Exception:
        #print(f"\t\t getResult KeyError: {result}")
        return 0

def get_result_data(result, result_type = 'EX1data'):
    try:
        return [ res['code'] for res in result[result_type]]
    except Exception:
        return []

def compare_numbers(numbers):
    min_number = min(numbers)
    max_number = max(numbers)
    if max_number - min_number > 10:
        return f"{numbers}  {Style.RED}Min: {min_number}, Max: {max_number}{Style.RESET}"
    else:
        return f"{numbers}"

def print_list_in_columns(strings):
    terminal_width = shutil.get_terminal_size().columns - 16
    column_width = terminal_width // len(strings)
    print('\t\t', end='')
    for i, string in enumerate(strings):
        end_char = '\n' if (i + 1) % len(strings) == 0 else ''
        print(f"{string:<{column_width}}", end=end_char)

#[[products from data set1],[products from data set2], [.....]]
def display_product_data(product_data):
    set_count = len(product_data)
    max_index = max([len(d) for d in product_data])
    lines = []
    for i in range(max_index):
        product_line = []
        for j in range(set_count):
            try:
                p = product_data[j][i]
                product_line.append(f"{p['productCode']} {p['generalName']} {p['technicalName']}")
            except IndexError:
                product_line.append("")
        lines.append(product_line)
    for l in lines:
        print_list_in_columns(l)

def get_product_data(sales_org, product_codes, lang = "fi"):
    url = f'{commerceplus_url}/v2/{sales_org}/products/texts'
    proxy= proxy_settings if 'prod' in commerceplus_url else None
    query = product_codes
    headers = {
    "Accept": "application/json",
    "X-Locale": lang
    }
    response = requests.post(url, json=query, headers=headers, proxies=proxy)
    if response.status_code == 200:
        resp = response.json()
        return [resp[code] for code in product_codes]
    else:
        print(f'Error: {response.status_code}')
        print(response.text)


def print_stats(results_data, sta_result):
    res = [sta_result] + results_data
    res_pairs = [(a, b) for idx, a in enumerate(res) for b in res[idx + 1:]]
    print(f' ≥≥ EX1 {len(sta_result)} ≤≤ ', end='')
    for pair in res_pairs:
        print(f' 﫷 [{overlap(*pair)},{levenshtein_distance(*pair)},{similarity_score(*pair)}]', end='')
    print()


def main():
    sales_org = '1000'
    lang = 'fi'
    verbose = True
    if len(sys.argv) < 3:
        print("Usage: python display_result.py <pickle_file_path> <pickle_file_path> ...")
        sys.exit(1)

    try:
        filenames = sys.argv[1:]
        print('Files:',filenames)
        results = [pickle.load(open(filename, "rb"))['result'] for filename in filenames]

        # print("Results:", results);
        sum_of_keys = set()
        for result in results:
            sum_of_keys.update(result.keys())

        for key in sorted(sum_of_keys):
            result_count = []
            result_data = []
            for result in results:
                value = result.get(key, 0)
                result_count.append(get_result_count(value))
                result_data.append(get_result_data(value))
            print_separator()
            print(f"{Style.GREEN}{Style.BOLD}{key}{Style.RESET}: {compare_numbers(result_count)}", end='')
            print_stats(result_data, get_result_data(result=results[0].get(key,0),result_type='EX1data'))
            ll = '\n\t\t'.join(f"{sublist}" for sublist in result_data)
            if verbose:
                product_data = [get_product_data(sales_org, code_set, lang) for code_set in result_data]
                display_product_data(product_data)
            else:
                print(f"\t\t{ll}")


        print('Files:',filenames)
    except Exception as e:
        print(f"An error occurred: {e}")
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
