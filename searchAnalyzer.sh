#!/bin/bash
#
# chech how a given word is processed by different analyzers

# OpenSearch server details
OPENSEARCH_HOST="localhost"
OPENSEARCH_PORT="9202"
INDEX_NAME="ex1_products_1000"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

read -r -d '' ANALYZERS << EOA
conditional_stemmed_ngrams_fi
conditional_stemmed_prefix_fi
keyword_lowercase
keyword_prefix3
keyword_prefix5
name_specification_fragments
path_analyzer
standard_stemmed_en
standard_stemmed_et
standard_stemmed_fi
standard_stemmed_lv
standard_stemmed_ngrams_en
standard_stemmed_ngrams_et
standard_stemmed_ngrams_fi
standard_stemmed_ngrams_lv
standard_stemmed_ngrams_no
standard_stemmed_ngrams_sv
standard_stemmed_no
standard_stemmed_subwords_en
standard_stemmed_subwords_et
standard_stemmed_subwords_fi
standard_stemmed_subwords_lv
standard_stemmed_subwords_no
standard_stemmed_subwords_sv
standard_stemmed_sv
whitespace_lowercase
whitespace_stemmed_en
whitespace_stemmed_et
whitespace_stemmed_fi
whitespace_stemmed_lv
whitespace_stemmed_no
whitespace_stemmed_prefix_en
whitespace_stemmed_prefix_et
whitespace_stemmed_prefix_fi
whitespace_stemmed_prefix_lv
whitespace_stemmed_prefix_no
whitespace_stemmed_prefix_sv
whitespace_stemmed_sv
EOA

# Sample text to analyze
TEXT_TO_ANALYZE=${1:-copper tubes}

BASE_URL="http://${OPENSEARCH_HOST}:${OPENSEARCH_PORT}/${INDEX_NAME}/_analyze"

for ANALYZER_NAME in $ANALYZERS; do

	JSON_PAYLOAD='{
    "analyzer": "'${ANALYZER_NAME}'",
    "text": "'${TEXT_TO_ANALYZE}'"
  }'
	TOKENS=$(curl -s -X POST -H "Content-Type: application/json" -d "${JSON_PAYLOAD}" "${BASE_URL}" | jq -r '.tokens | map(.token) | join(", ")')
	echo -e "$RED $ANALYZER_NAME $NC -> $YELLOW $TEXT_TO_ANALYZE $NC -> $GREEN [$TOKENS] $NC"
	echo "_____________________________________________________________________________________________________"
done
